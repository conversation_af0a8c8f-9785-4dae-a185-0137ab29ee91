# A Simple Basic Synthesizer for the AI Thinker Audio Kit

A [detailed description can be found in my blog](https://www.pschatzmann.ch/home/<USER>/12/17/ai-thinker-audio-kit-building-a-simple-synthesizer-with-the-audiotools-library/) ...

### Dependencies

You need to install the following libraries:

- [Arduino Audio Tools](https://github.com/pschatzmann/arduino-audio-tools)
- [Arduino Audio Tools - Midi](https://github.com/pschatzmann/arduino-midi)

