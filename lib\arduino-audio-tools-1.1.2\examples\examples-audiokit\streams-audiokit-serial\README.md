
# Stream Input from an AudioKit to CSV Output

## General Description:

We implement a AudioKit source: We stream the sound input which we read in from the I2S interface and display it on the Arduino Serial Plotter. 

<img src="https://pschatzmann.github.io/Resources/img/audio-toolkit.png" alt="Audio Kit" />

### Dependencies

You need to install the following libraries:

- https://github.com/pschatzmann/arduino-audio-tools
- https://github.com/pschatzmann/arduino-audio-driver
