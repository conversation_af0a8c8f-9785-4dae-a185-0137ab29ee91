# Implementing a Synthesizer using the STK Framwork

We can use the STK framework to implement a Synthesizer which receives Midi Messages and translates them 
into sound. Here we use a Clarinet...

For [further info see my blog](https://www.pschatzmann.ch/home/<USER>/12/21/ai-thinker-audiokit-a-simply-synthesizer-with-stk/)

### Dependencies

- https://github.com/pschatzmann/arduino-audio-tools
- https://github.com/pschatzmann/arduino-audio-driver
- https://github.com/pschatzmann/arduino-midi
- https://github.com/pschatzmann/Arduino-STK
